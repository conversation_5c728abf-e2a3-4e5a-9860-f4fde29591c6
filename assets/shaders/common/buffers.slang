#ifndef BUFFERS_SLANG
#define BUFFERS_SLANG

// GPU buffer declarations
#include "data_structures.slang"

// --- GPU Buffers (using global bindings) ---

StructuredBuffer<MeshletDescriptor> g_meshlets         : register(t0);
StructuredBuffer<MeshData>          g_meshData         : register(t2);
StructuredBuffer<BasicVertex>       g_meshVertices     : register(t0, space1);
StructuredBuffer<uint>              g_meshletVertices  : register(t2, space1);
StructuredBuffer<uint8_t>           g_meshletTriangles : register(t3, space1);

// --- Constant Buffers ---

// Constant buffer for instance-specific data
cbuffer InstanceData : register(b1) {
    float4x4 modelViewProjectionMatrix;
    float4x4 inverseModelViewMatrix;
    float4x4 normalMatrix;
};

#endif // BUFFERS_SLANG
